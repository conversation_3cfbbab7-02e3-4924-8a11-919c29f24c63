import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码加密和验证Demo
 * 演示Spring Security BCrypt密码编码器的使用
 */
public class PasswordDemo {
    
    public static void main(String[] args) {
        // 创建密码编码器
        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        
        // 模拟用户注册时的密码加密
        System.out.println("=== 用户注册 - 密码加密 ===");
        String rawPassword = "123456";  // 用户输入的明文密码
        String encodedPassword = passwordEncoder.encode(rawPassword);
        
        System.out.println("明文密码: " + rawPassword);
        System.out.println("加密密码: " + encodedPassword);
        System.out.println("加密密码长度: " + encodedPassword.length());
        
        // 模拟用户登录时的密码验证
        System.out.println("\n=== 用户登录 - 密码验证 ===");
        String loginPassword = "123456";  // 用户登录时输入的密码
        boolean isMatch = passwordEncoder.matches(loginPassword, encodedPassword);
        
        System.out.println("登录密码: " + loginPassword);
        System.out.println("数据库密码: " + encodedPassword);
        System.out.println("密码匹配: " + isMatch);
        
        // 测试错误密码
        System.out.println("\n=== 错误密码测试 ===");
        String wrongPassword = "654321";
        boolean isWrongMatch = passwordEncoder.matches(wrongPassword, encodedPassword);
        
        System.out.println("错误密码: " + wrongPassword);
        System.out.println("密码匹配: " + isWrongMatch);
        
        // 演示同一密码多次加密的结果不同
        System.out.println("\n=== BCrypt特性演示 ===");
        String password = "123456";
        String encoded1 = passwordEncoder.encode(password);
        String encoded2 = passwordEncoder.encode(password);
        String encoded3 = passwordEncoder.encode(password);
        
        System.out.println("同一密码多次加密结果：");
        System.out.println("第1次: " + encoded1);
        System.out.println("第2次: " + encoded2);
        System.out.println("第3次: " + encoded3);
        
        System.out.println("\n验证结果：");
        System.out.println("密码1匹配: " + passwordEncoder.matches(password, encoded1));
        System.out.println("密码2匹配: " + passwordEncoder.matches(password, encoded2));
        System.out.println("密码3匹配: " + passwordEncoder.matches(password, encoded3));
        
        // 演示BCrypt密码的结构
        System.out.println("\n=== BCrypt密码结构分析 ===");
        String bcryptPassword = passwordEncoder.encode("demo123");
        System.out.println("BCrypt密码: " + bcryptPassword);
        System.out.println("密码结构说明:");
        System.out.println("$2a$10$... 格式说明:");
        System.out.println("  $2a    - BCrypt算法版本");
        System.out.println("  $10    - 成本因子(工作因子)，值越大越安全但越慢");
        System.out.println("  接下来22个字符 - 盐值(Salt)");
        System.out.println("  最后31个字符 - 实际的哈希值");
    }
}
