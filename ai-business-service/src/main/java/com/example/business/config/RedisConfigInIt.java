package com.example.business.config;

import com.example.business.entity.AiChatTemplate;
import com.example.business.entity.AiConfig;
import com.example.business.entity.AiModelConfig;
import com.example.business.service.IAiChatTemplateService;
import com.example.business.service.IAiConfigService;
import com.example.business.service.IAiModelConfigService;
import com.example.common.entity.Enum.RedisCatchEnum;
import com.example.common.utils.GenericRedisUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 初始化Redis数据
 * 使用新的泛型Redis工具类，提供类型安全的操作
 */
@Slf4j
@RequiredArgsConstructor
//@Component
public class RedisConfigInIt {

    private final IAiModelConfigService iAiModelConfigService;

    private final GenericRedisUtil redisUtil;

    private final IAiConfigService iAiConfigService;

    private final IAiChatTemplateService iAiChatTemplateService;

    /**
     * 初始AI模型配置表
     */
    @PostConstruct
    public void initAiModelConfig() {
        //默认先删除
        redisUtil.delete(RedisCatchEnum.AI_MODEL_CONFIG.getTitle());
        log.info("开始初始化AI模型配置表到Redis");
        List<AiModelConfig> aiModelConfigList = iAiModelConfigService.list();
        if(!CollectionUtils.isEmpty(aiModelConfigList)){
            // 使用泛型方法，一次性存储整个列表，性能更好
            boolean success = redisUtil.lSet(RedisCatchEnum.AI_MODEL_CONFIG.getTitle(), aiModelConfigList);
            if (success) {
                log.info("AI模型配置表初始化完成，成功缓存 {} 条记录", aiModelConfigList.size());
            } else {
                log.error("AI模型配置表初始化失败");
            }
        } else {
            log.warn("未找到AI模型配置数据，跳过初始化");
        }
    }

    /**
     * 初始化AI配置表到Redis
     */
    @PostConstruct
    public void initAiConfig() {
        //默认先删除
        redisUtil.delete(RedisCatchEnum.AI_CONFIG.getTitle());
        log.info("开始初始化AI配置表到Redis");
        try {
            List<AiConfig> aiConfigList = iAiConfigService.list();
            if (!CollectionUtils.isEmpty(aiConfigList)) {
                // 使用正确的Redis键和泛型方法
                boolean success = redisUtil.listRightPushAll(
                    RedisCatchEnum.AI_CONFIG.getTitle(),
                    aiConfigList
                );
                if (success) {
                    log.info("AI配置表初始化完成，成功缓存 {} 条记录", aiConfigList.size());
                } else {
                    log.error("AI配置表初始化失败");
                }
            } else {
                log.warn("未找到AI配置数据，跳过初始化");
            }
        } catch (Exception e) {
            log.error("初始化AI配置表时发生异常", e);
        }
    }

    /**
     * 初始化AI会话模板表到Redis
     */
    @PostConstruct
    public void initAiChatTemplate() {
        //默认先删除
        redisUtil.delete(RedisCatchEnum.AI_CHAT_TEMPLATE.getTitle());
        log.info("开始初始化AI会话模板表到Redis");
        try {
            List<AiChatTemplate> aiChatTemplatesList = iAiChatTemplateService.list();
            if (!CollectionUtils.isEmpty(aiChatTemplatesList)) {
                // 使用泛型方法，一次性存储整个列表
                boolean success = redisUtil.listRightPushAll(
                    RedisCatchEnum.AI_CHAT_TEMPLATE.getTitle(),
                    aiChatTemplatesList
                );
                if (success) {
                    log.info("AI会话模板表初始化完成，成功缓存 {} 条记录", aiChatTemplatesList.size());
                } else {
                    log.error("AI会话模板表初始化失败");
                }
            } else {
                log.warn("未找到AI会话模板数据，跳过初始化");
            }
        } catch (Exception e) {
            log.error("初始化AI会话模板表时发生异常", e);
        }
    }
}
