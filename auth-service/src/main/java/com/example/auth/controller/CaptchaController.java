package com.example.auth.controller;

import com.example.auth.dto.CaptchaRequest;
import com.example.auth.dto.CaptchaResponse;
import com.example.auth.service.CaptchaService;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 验证码控制器
 */
@Slf4j
@RestController
@RequestMapping("/captcha")
@RequiredArgsConstructor
public class CaptchaController {

    private final CaptchaService captchaService;

    /**
     * 生成滑动验证码
     */
    @GetMapping("/slide")
    public Result<CaptchaResponse> generateSlideCaptcha() {
        log.info("请求生成滑动验证码");
        CaptchaResponse response = captchaService.generateSlideCaptcha();
        return Result.success("验证码生成成功", response);
    }

    /**
     * 验证滑动验证码
     */
    @PostMapping("/verify")
    public Result<Boolean> verifySlideCaptcha(@Valid @RequestBody CaptchaRequest request) {
        log.info("请求验证滑动验证码，ID: {}", request.getCaptchaId());
        boolean isValid = captchaService.verifySlideCaptcha(request);
        
        if (isValid) {
            return Result.success("验证成功", true);
        } else {
            return Result.error("验证失败，请重试");
        }
    }

    /**
     * 刷新验证码
     */
    @PostMapping("/refresh")
    public Result<CaptchaResponse> refreshCaptcha(@RequestParam(required = false) String captchaId) {
        log.info("请求刷新验证码，ID: {}", captchaId);
        CaptchaResponse response = captchaService.refreshCaptcha(captchaId);
        return Result.success("验证码刷新成功", response);
    }
}
