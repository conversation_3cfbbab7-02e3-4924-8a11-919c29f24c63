package com.example.auth.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 验证码请求DTO
 */
@Data
public class CaptchaRequest {

    /**
     * 验证码ID
     */
    @NotBlank(message = "验证码ID不能为空")
    private String captchaId;

    /**
     * 滑动距离（像素）
     */
    private Integer slideDistance;

    /**
     * 验证码类型：slide-滑动验证码, click-点击验证码
     */
    private String captchaType = "slide";

    /**
     * 验证轨迹数据（可选，用于行为分析）
     */
    private String trackData;
}
