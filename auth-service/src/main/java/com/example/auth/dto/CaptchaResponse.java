package com.example.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaptchaResponse {

    /**
     * 验证码ID
     */
    private String captchaId;

    /**
     * 背景图片Base64编码
     */
    private String backgroundImage;

    /**
     * 滑块图片Base64编码
     */
    private String sliderImage;

    /**
     * 滑块初始Y坐标
     */
    private Integer sliderY;

    /**
     * 验证码过期时间（秒）
     */
    private Long expireTime;

    /**
     * 验证码类型
     */
    private String captchaType;
}
