package com.example.auth.service;

import com.example.auth.dto.CaptchaRequest;
import com.example.auth.dto.CaptchaResponse;

/**
 * 验证码服务接口
 */
public interface CaptchaService {

    /**
     * 生成滑动验证码
     * @return 验证码响应数据
     */
    CaptchaResponse generateSlideCaptcha();

    /**
     * 验证滑动验证码
     * @param request 验证请求
     * @return 验证结果
     */
    boolean verifySlideCaptcha(CaptchaRequest request);

    /**
     * 刷新验证码
     * @param captchaId 验证码ID
     * @return 新的验证码响应数据
     */
    CaptchaResponse refreshCaptcha(String captchaId);

    /**
     * 清理过期验证码
     */
    void cleanExpiredCaptcha();
}
