package com.example.auth.service.impl;

import com.example.auth.dto.CaptchaRequest;
import com.example.auth.dto.LoginResponse;
import com.example.auth.dto.RegisterRequest;
import com.example.auth.feign.UserFeign;
import com.example.auth.service.AuthService;
import com.example.auth.service.CaptchaService;
import com.example.common.result.Result;
import com.example.common.utils.JwtUtils;
import com.example.common.dto.UserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserFeign userFeign;
    private final CaptchaService captchaService;

    @Override
    public LoginResponse login(LoginRequest request) {
        try {
            log.info("User login attempt: {}", request.getUsername());

            // 1. 验证滑动验证码
            CaptchaRequest captchaRequest = new CaptchaRequest();
            captchaRequest.setCaptchaId(request.getCaptchaId());
            captchaRequest.setSlideDistance(request.getSlideDistance());

            if (!captchaService.verifySlideCaptcha(captchaRequest)) {
                throw new RuntimeException("验证码验证失败");
            }

            // 2. 获取用户信息
            Result<UserDto> userResult = userFeign.getUserByUsername(request.getUsername());
            if (userResult.getCode() != 200 || userResult.getData() == null) {
                throw new RuntimeException("用户不存在");
            }

            UserDto user = userResult.getData();

            // 3. 验证密码
            Result<Boolean> validateResult = userFeign.validatePassword(request.getUsername(), request.getPassword());
            if (validateResult.getCode() != 200 || !validateResult.getData()) {
                throw new RuntimeException("用户名或密码错误");
            }

            // 4. 生成JWT Token
            String accessToken = JwtUtils.generateToken(request.getUsername(), user.getId());
            String refreshToken = JwtUtils.generateRefreshToken(request.getUsername(), user.getId());

            // 5. 更新最后登录时间（异步，不影响主流程）
            try {
                userFeign.updateLastLoginTime(user.getId());
            } catch (Exception e) {
                log.warn("Failed to update last login time for user: {}", request.getUsername(), e);
            }

            // 5. 构建响应
            LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .nickname(user.getNickname())
                    .email(user.getEmail())
                    .avatar(user.getAvatar())
                    .roles(user.getRoles())
                    .build();

            LoginResponse response = LoginResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(86400L) // 24小时
                    .userInfo(userInfo)
                    .build();

            log.info("User login successful: {}", request.getUsername());
            return response;

        } catch (Exception e) {
            log.error("Login failed for user: {}", request.getUsername(), e);
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }

    @Override
    public void register(RegisterRequest request) {
        try {
            log.info("User registration attempt: {}", request.getUsername());

            // 1. 检查用户名是否已存在
            Result<Boolean> usernameExistsResult = userFeign.existsByUsername(request.getUsername());
            if (usernameExistsResult.getCode() == 200 && usernameExistsResult.getData()) {
                throw new RuntimeException("用户名已存在");
            }

            // 2. 检查邮箱是否已存在
            if (request.getEmail() != null && !request.getEmail().isEmpty()) {
                Result<Boolean> emailExistsResult = userFeign.existsByEmail(request.getEmail());
                if (emailExistsResult.getCode() ==200 && emailExistsResult.getData()) {
                    throw new RuntimeException("邮箱已被使用");
                }
            }

            // 3. 创建用户对象
            UserDto user = new UserDto()
                    .setUsername(request.getUsername())
                    .setPassword(request.getPassword()) // 在用户服务中会进行加密
                    .setEmail(request.getEmail())
                    .setNickname(request.getNickname())
                    .setPhone(request.getPhone())
                    .setStatus(1) // 默认启用
                    .setRoles("USER"); // 默认角色

            // 4. 调用用户服务创建用户
            Result<UserDto> createResult = userFeign.createUser(user);
            if (createResult.getCode() != 200) {
                throw new RuntimeException("用户创建失败: " + createResult.getMessage());
            }

            log.info("User registration successful: {}", request.getUsername());

        } catch (Exception e) {
            log.error("Registration failed for user: {}", request.getUsername(), e);
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateToken(String token) {
        try {
            String username = JwtUtils.getUsernameFromToken(token);
            return JwtUtils.validateToken(token, username);
        } catch (Exception e) {
            log.error("Token validation failed", e);
            return false;
        }
    }

    @Override
    public String refreshToken(String token) {
        try {
            String username = JwtUtils.getUsernameFromToken(token);
            Long userId = JwtUtils.getUserIdFromToken(token);
            return JwtUtils.generateToken(username, userId);
        } catch (Exception e) {
            log.error("Token refresh failed", e);
            throw new RuntimeException("Token刷新失败: " + e.getMessage());
        }
    }
}
