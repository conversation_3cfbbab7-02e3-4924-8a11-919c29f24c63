package com.example.auth.service.impl;

import com.example.auth.dto.CaptchaRequest;
import com.example.auth.dto.CaptchaResponse;
import com.example.auth.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final RedisTemplate<String, Object> redisTemplate;

    // 验证码配置
    private static final int CAPTCHA_WIDTH = 300;
    private static final int CAPTCHA_HEIGHT = 150;
    private static final int SLIDER_WIDTH = 60;
    private static final int SLIDER_HEIGHT = 60;
    private static final int EXPIRE_TIME = 300; // 5分钟过期
    private static final String CAPTCHA_KEY_PREFIX = "captcha:";

    @Override
    public CaptchaResponse generateSlideCaptcha() {
        try {
            log.info("开始生成滑动验证码");
            
            String captchaId = UUID.randomUUID().toString();
            
            // 生成随机滑块位置
            Random random = new Random();
            int sliderX = random.nextInt(CAPTCHA_WIDTH - SLIDER_WIDTH - 50) + 50; // 确保滑块不在边缘
            int sliderY = random.nextInt(CAPTCHA_HEIGHT - SLIDER_HEIGHT - 20) + 10;
            
            // 生成背景图片
            BufferedImage backgroundImage = generateBackgroundImage();
            
            // 生成滑块图片和缺口
            BufferedImage sliderImage = generateSliderImage(backgroundImage, sliderX, sliderY);
            
            // 在背景图上创建缺口
            createSliderHole(backgroundImage, sliderX, sliderY);
            
            // 转换为Base64
            String backgroundBase64 = imageToBase64(backgroundImage);
            String sliderBase64 = imageToBase64(sliderImage);
            
            // 存储验证码信息到Redis
            String cacheKey = CAPTCHA_KEY_PREFIX + captchaId;
            redisTemplate.opsForValue().set(cacheKey, sliderX, EXPIRE_TIME, TimeUnit.SECONDS);
            
            CaptchaResponse response = CaptchaResponse.builder()
                    .captchaId(captchaId)
                    .backgroundImage(backgroundBase64)
                    .sliderImage(sliderBase64)
                    .sliderY(sliderY)
                    .expireTime((long) EXPIRE_TIME)
                    .captchaType("slide")
                    .build();
            
            log.info("滑动验证码生成成功，ID: {}", captchaId);
            return response;
            
        } catch (Exception e) {
            log.error("生成滑动验证码失败", e);
            throw new RuntimeException("生成验证码失败");
        }
    }

    @Override
    public boolean verifySlideCaptcha(CaptchaRequest request) {
        try {
            log.info("开始验证滑动验证码，ID: {}", request.getCaptchaId());
            
            String cacheKey = CAPTCHA_KEY_PREFIX + request.getCaptchaId();
            Object cachedX = redisTemplate.opsForValue().get(cacheKey);
            
            if (cachedX == null) {
                log.warn("验证码已过期或不存在，ID: {}", request.getCaptchaId());
                return false;
            }
            
            int correctX = (Integer) cachedX;
            int userX = request.getSlideDistance();
            
            // 允许5像素的误差
            boolean isValid = Math.abs(correctX - userX) <= 5;
            
            // 验证后删除验证码（无论成功失败）
            redisTemplate.delete(cacheKey);
            
            log.info("验证码验证结果: {}, 正确位置: {}, 用户滑动: {}", isValid, correctX, userX);
            return isValid;
            
        } catch (Exception e) {
            log.error("验证滑动验证码失败", e);
            return false;
        }
    }

    @Override
    public CaptchaResponse refreshCaptcha(String captchaId) {
        // 删除旧的验证码
        if (captchaId != null) {
            redisTemplate.delete(CAPTCHA_KEY_PREFIX + captchaId);
        }
        // 生成新的验证码
        return generateSlideCaptcha();
    }

    @Override
    public void cleanExpiredCaptcha() {
        // Redis的TTL会自动清理过期数据，这里可以添加额外的清理逻辑
        log.info("清理过期验证码任务执行");
    }

    /**
     * 生成背景图片
     */
    private BufferedImage generateBackgroundImage() {
        BufferedImage image = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制渐变背景
        GradientPaint gradient = new GradientPaint(0, 0, new Color(135, 206, 250), 
                                                  CAPTCHA_WIDTH, CAPTCHA_HEIGHT, new Color(70, 130, 180));
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
        
        // 添加一些装饰线条
        Random random = new Random();
        g2d.setColor(new Color(255, 255, 255, 100));
        for (int i = 0; i < 10; i++) {
            int x1 = random.nextInt(CAPTCHA_WIDTH);
            int y1 = random.nextInt(CAPTCHA_HEIGHT);
            int x2 = random.nextInt(CAPTCHA_WIDTH);
            int y2 = random.nextInt(CAPTCHA_HEIGHT);
            g2d.drawLine(x1, y1, x2, y2);
        }
        
        g2d.dispose();
        return image;
    }

    /**
     * 生成滑块图片
     */
    private BufferedImage generateSliderImage(BufferedImage backgroundImage, int x, int y) {
        BufferedImage sliderImage = new BufferedImage(SLIDER_WIDTH, SLIDER_HEIGHT, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = sliderImage.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 从背景图片中提取滑块区域
        BufferedImage sliderArea = backgroundImage.getSubimage(x, y, SLIDER_WIDTH, SLIDER_HEIGHT);
        g2d.drawImage(sliderArea, 0, 0, null);
        
        // 绘制滑块边框
        g2d.setColor(new Color(255, 255, 255, 200));
        g2d.setStroke(new BasicStroke(2));
        g2d.drawRoundRect(1, 1, SLIDER_WIDTH - 2, SLIDER_HEIGHT - 2, 10, 10);
        
        g2d.dispose();
        return sliderImage;
    }

    /**
     * 在背景图上创建滑块缺口
     */
    private void createSliderHole(BufferedImage backgroundImage, int x, int y) {
        Graphics2D g2d = backgroundImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 创建半透明的缺口
        g2d.setColor(new Color(0, 0, 0, 100));
        g2d.fillRoundRect(x, y, SLIDER_WIDTH, SLIDER_HEIGHT, 10, 10);
        
        // 绘制缺口边框
        g2d.setColor(new Color(255, 255, 255, 150));
        g2d.setStroke(new BasicStroke(2));
        g2d.drawRoundRect(x, y, SLIDER_WIDTH, SLIDER_HEIGHT, 10, 10);
        
        g2d.dispose();
    }

    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
    }
}
