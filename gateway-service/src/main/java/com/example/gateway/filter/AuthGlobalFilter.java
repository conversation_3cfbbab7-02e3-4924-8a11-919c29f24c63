package com.example.gateway.filter;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 认证全局过滤器
 */
@Slf4j
@Component
public class AuthGlobalFilter implements GlobalFilter, Ordered {

    // JWT密钥配置
    @Value("${jwt.secret:mySecretKeyForJWTTokenGenerationAndValidation}")
    private String jwtSecret;

    // 不需要认证的路径
    private static final List<String> SKIP_AUTH_PATHS = Arrays.asList(
            "/auth/login",
            "/auth/register",
            "/auth/validate",
            "/auth/refresh",
            "/actuator/**"  // 健康检查端点
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        log.info("请求路径: {}", path);

        // 检查是否需要跳过认证
        if (shouldSkipAuth(path)) {
            log.info("跳过认证: {}", path);
            return chain.filter(exchange);
        }

        // 获取Authorization头
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
            log.warn("缺少或无效的Authorization头: {}", authHeader);
            return unauthorized(exchange.getResponse());
        }

        // 提取token
        String token = authHeader.substring(7);

        try {
            // 验证token（不依赖数据库，只验证JWT签名和有效期）
            Claims claims = validateJwtToken(token);
            if (claims == null) {
                log.warn("Token验证失败: {}", token);
                return unauthorized(exchange.getResponse());
            }

            // 从Token中提取用户信息
            String username = claims.getSubject();
            String userId = claims.get("userId", String.class);

            // 将用户信息添加到请求头
            ServerHttpRequest mutatedRequest = request.mutate()
                    .header("X-User-Id", userId)
                    .header("X-Username", username)
                    .build();

            log.debug("Token验证成功，用户: {}, ID: {}", username, userId);
            return chain.filter(exchange.mutate().request(mutatedRequest).build());

        } catch (Exception e) {
            log.error("Token验证异常: ", e);
            return unauthorized(exchange.getResponse());
        }
    }

    /**
     * 验证JWT Token（不依赖数据库）
     */
    private Claims validateJwtToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("JWT Token解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查是否应该跳过认证
     */
    private boolean shouldSkipAuth(String path) {
        return SKIP_AUTH_PATHS.stream().anyMatch(skipPath -> {
            if (skipPath.endsWith("/**")) {
                // 处理通配符路径，如 /actuator/**
                return path.startsWith(skipPath.substring(0, skipPath.length() - 3));
            } else {
                // 精确匹配路径，支持带查询参数和尾部斜杠的情况
                return path.equals(skipPath) || path.startsWith(skipPath + "/") || path.startsWith(skipPath + "?");
            }
        });
    }

    /**
     * 返回未授权响应
     */
    private Mono<Void> unauthorized(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        return response.setComplete();
    }

    @Override
    public int getOrder() {
        return -100; // 优先级高，早执行
    }
}
