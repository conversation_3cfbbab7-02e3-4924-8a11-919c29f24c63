package com.example.user.service.impl;

import com.example.user.entity.User;
import com.example.user.mapper.UserMapper;
import com.example.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public User getUserById(Long id) {
        log.info("Getting user by id: {}", id);
        return userMapper.selectById(id);
    }

    @Override
    public List<User> getAllUsers() {
        log.info("Getting all users");
        return userMapper.selectList(null);
    }

    @Override
    public void updateUser(User user) {
        log.info("Updating user: {}", user.getId());
        userMapper.updateById(user);
    }

    @Override
    public void deleteUser(Long id) {
        log.info("Deleting user: {}", id);
        userMapper.deleteById(id);
    }

    @Override
    public User getUserByUsername(String username) {
        log.info("Getting user by username: {}", username);
        return userMapper.selectByUsername(username);
    }

    @Override
    public User getUserByEmail(String email) {
        log.info("Getting user by email: {}", email);
        return userMapper.selectByEmail(email);
    }

    @Override
    @Transactional
    public User createUser(User user) {
        log.info("Creating user: {}", user.getUsername());

        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        if (user.getRoles() == null) {
            user.setRoles("USER"); // 默认角色
        }

        // 加密密码
        if (user.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        userMapper.insert(user);
        log.info("User created successfully: {}", user.getUsername());
        return user;
    }

    @Override
    public boolean validatePassword(String username, String password) {
        log.info("Validating password for user: {}", username);
        User user = getUserByUsername(username);
        if (user == null) {
            log.warn("User not found: {}", username);
            return false;
        }

        boolean isValid = passwordEncoder.matches(password, user.getPassword());
        log.info("Password validation result for user {}: {}", username, isValid);
        return isValid;
    }

    @Override
    public void updateLastLoginTime(Long userId) {
        log.info("Updating last login time for user: {}", userId);
        userMapper.updateLastLoginTime(userId);
    }

    @Override
    public boolean existsByUsername(String username) {
        log.info("Checking if username exists: {}", username);
        User user = userMapper.selectByUsername(username);
        return user != null;
    }

    @Override
    public boolean existsByEmail(String email) {
        log.info("Checking if email exists: {}", email);
        User user = userMapper.selectByEmail(email);
        return user != null;
    }
}
