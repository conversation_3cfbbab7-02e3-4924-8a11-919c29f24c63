# ==========================================
# 用户管理服务配置文件
# 负责用户信息的CRUD操作和用户相关业务
# ==========================================

# 服务器配置
server:
  port: 8084  # 用户服务端口
  servlet:
    context-path: # 应用上下文路径
    encoding:
      charset: UTF-8  # 字符编码
      enabled: true
      force: true

# Spring框架配置
spring:
  application:
    name: user-service  # 服务名称，用于服务注册和发现



  # 数据源配置 - MySQL数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver  # MySQL 8.x驱动
    url: ******************************************************************************************************************************************************
    username: root  # 数据库用户名
    password: root  # 数据库密码
    # HikariCP连接池配置
    hikari:
      maximum-pool-size: 20  # 连接池最大连接数
      minimum-idle: 5  # 连接池最小空闲连接数
      connection-timeout: 30000  # 连接超时时间(毫秒)
      idle-timeout: 600000  # 空闲连接超时时间(毫秒)
      max-lifetime: 1800000  # 连接最大生存时间(毫秒)
      leak-detection-threshold: 60000  # 连接泄漏检测阈值(毫秒)

  # Redis配置 - 用于缓存用户信息
  data:
    redis:
      host: ************  # Redis服务器地址
      port: 6379  # Redis端口
      database: 2  # 使用数据库2，避免与其他服务冲突
      password:  # Redis密码，如果设置了密码请填写
      timeout: 10000ms  # 连接超时时间
      # Lettuce连接池配置
      lettuce:
        pool:
          max-active: 8  # 连接池最大连接数
          max-wait: -1ms  # 连接池最大阻塞等待时间
          max-idle: 8  # 连接池最大空闲连接数
          min-idle: 0  # 连接池最小空闲连接数
        shutdown-timeout: 100ms  # 关闭超时时间

# MyBatis Plus配置 - ORM框架配置
mybatis-plus:
  # MyBatis配置
  configuration:
    map-underscore-to-camel-case: true  # 下划线转驼峰命名
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出到控制台
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      id-type: AUTO  # 主键生成策略：自增
      logic-delete-field: deleted  # 逻辑删除字段名
      logic-delete-value: 1  # 逻辑删除值(已删除)
      logic-not-delete-value: 0  # 逻辑删除值(未删除)
  # 实体类包路径
  type-aliases-package: com.example.user.entity

# OpenFeign配置 - 微服务间调用（统一配置）
feign:
  client:
    config:
      default:  # 默认配置，应用于所有Feign客户端
        connect-timeout: 5000  # 连接超时时间(毫秒)
        read-timeout: 10000  # 读取超时时间(毫秒)
        logger-level: basic  # 日志级别：NONE, BASIC, HEADERS, FULL
      auth-service:  # 针对认证服务的特殊配置
        connect-timeout: 3000  # 认证服务连接超时
        read-timeout: 8000  # 认证服务读取超时
        logger-level: basic
  # 启用压缩
  compression:
    request:
      enabled: true  # 启用请求压缩
      mime-types: text/xml,application/xml,application/json  # 压缩的MIME类型
      min-request-size: 2048  # 最小压缩请求大小
    response:
      enabled: true  # 启用响应压缩
  # 启用Circuit Breaker支持
  circuitbreaker:
    enabled: true  # 启用Circuit Breaker以支持fallback功能
    alphanumeric-ids:
      enabled: true  # 启用字母数字ID
  # 禁用Hystrix，使用Resilience4j替代
  hystrix:
    enabled: false



# 日志配置 - 简化版本，减少冗余信息
logging:
  level:
    # 根日志级别设置为INFO
    root: info
    # 用户服务核心包使用INFO级别
    com.example.user: info  # 自定义包的日志级别
    # 框架日志设置为WARN级别
    org.springframework.cloud.openfeign: warn  # OpenFeign日志级别
    org.mybatis: warn  # MyBatis日志级别
    com.baomidou.mybatisplus: warn  # MyBatis Plus日志级别
    # 数据库连接池日志设置为ERROR级别
    com.zaxxer.hikari: error
    # Nacos日志设置为WARN级别
    com.alibaba.nacos: warn
  pattern:
    # 简化的日志输出格式
    console: "%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{50} - %msg%n"
  file:
    name: logs/user-service.log  # 日志文件路径
    max-size: 50MB  # 减小单个日志文件大小
    max-history: 15  # 减少保留的日志文件数量

# 管理端点配置 - 用于健康检查和监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus  # 暴露的端点
  endpoint:
    health:
      show-details: always  # 显示健康检查详情
  metrics:
    export:
      prometheus:
        enabled: true  # 启用Prometheus指标导出
